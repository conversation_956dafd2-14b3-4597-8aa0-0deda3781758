#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI优化测试脚本
用于验证批量登录区域的优化效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt

# 模拟导入（如果实际模块不可用）
try:
    from src.ui.widgets.account_widget import AccountWidget
    REAL_WIDGET = True
except ImportError:
    print("实际模块不可用，创建模拟界面...")
    REAL_WIDGET = False
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton, 
        QComboBox, QSpinBox, QGroupBox, QFrame
    )

def create_mock_batch_login_widget():
    """创建模拟的批量登录组件"""
    # 批量操作区域 - 优化设计
    batch_group = QGroupBox("⚡ 批量登录")
    batch_group.setCheckable(True)
    batch_group.setChecked(True)
    batch_group.setStyleSheet("""
        QGroupBox {
            font-weight: bold;
            border: 1px solid #0078d4;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 15px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                stop:0 #f8fbff, stop:1 #e8f4fd);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
            color: #0078d4;
            font-size: 13px;
            font-weight: bold;
            background-color: white;
            border-radius: 4px;
        }
    """)
    batch_layout = QVBoxLayout(batch_group)
    batch_layout.setSpacing(15)
    batch_layout.setContentsMargins(15, 15, 15, 15)

    # 主要登录按钮区域 - 网格布局优化
    login_buttons_container = QWidget()
    login_buttons_layout = QGridLayout(login_buttons_container)
    login_buttons_layout.setSpacing(10)
    login_buttons_layout.setContentsMargins(0, 0, 0, 0)

    # 并发登录按钮
    concurrent_login_btn = QPushButton("⚡ 并发登录")
    concurrent_login_btn.setStyleSheet("""
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                stop:0 #0078d4, stop:1 #005a9e);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 13px;
            min-height: 18px;
            box-shadow: 0 2px 4px rgba(0,120,212,0.3);
        }
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                stop:0 #106ebe, stop:1 #004578);
            box-shadow: 0 3px 6px rgba(0,120,212,0.4);
        }
        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                stop:0 #005a9e, stop:1 #003d6b);
            box-shadow: 0 1px 2px rgba(0,120,212,0.2);
        }
    """)
    login_buttons_layout.addWidget(concurrent_login_btn, 0, 0)

    # 串行登录按钮
    batch_login_btn = QPushButton("🔑 串行登录")
    batch_login_btn.setStyleSheet("""
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                stop:0 #28a745, stop:1 #1e7e34);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 13px;
            min-height: 18px;
            box-shadow: 0 2px 4px rgba(40,167,69,0.3);
        }
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                stop:0 #218838, stop:1 #155724);
            box-shadow: 0 3px 6px rgba(40,167,69,0.4);
        }
        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                stop:0 #1e7e34, stop:1 #155724);
            box-shadow: 0 1px 2px rgba(40,167,69,0.2);
        }
    """)
    login_buttons_layout.addWidget(batch_login_btn, 0, 1)

    batch_layout.addWidget(login_buttons_container)

    # 分隔线
    separator = QFrame()
    separator.setFrameShape(QFrame.Shape.HLine)
    separator.setFrameShadow(QFrame.Shadow.Sunken)
    separator.setStyleSheet("""
        QFrame {
            color: #dee2e6;
            background-color: #dee2e6;
            border: none;
            max-height: 1px;
        }
    """)
    batch_layout.addWidget(separator)

    # 登录配置区域
    config_container = QWidget()
    config_container.setStyleSheet("""
        QWidget {
            background-color: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 0px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
    """)
    config_layout = QVBoxLayout(config_container)
    config_layout.setSpacing(12)
    config_layout.setContentsMargins(12, 12, 12, 12)

    # 配置标题
    config_title = QLabel("🔧 登录配置")
    config_title.setStyleSheet("""
        QLabel {
            font-weight: bold;
            font-size: 12px;
            color: #495057;
            border: none;
            background: transparent;
            padding: 0 0 5px 0;
            border-bottom: 1px solid #e9ecef;
        }
    """)
    config_layout.addWidget(config_title)

    # 配置选项网格布局
    config_grid_container = QWidget()
    config_grid_layout = QGridLayout(config_grid_container)
    config_grid_layout.setSpacing(8)
    config_grid_layout.setContentsMargins(0, 5, 0, 0)

    # 并发数量配置
    concurrent_label = QLabel("并发数量:")
    concurrent_label.setStyleSheet("""
        QLabel {
            font-size: 11px;
            color: #495057;
            font-weight: 500;
            border: none;
            background: transparent;
            padding: 2px 0;
            min-width: 70px;
        }
    """)
    config_grid_layout.addWidget(concurrent_label, 0, 0)

    concurrent_count_spin = QSpinBox()
    concurrent_count_spin.setRange(1, 10)
    concurrent_count_spin.setValue(3)
    concurrent_count_spin.setStyleSheet("""
        QSpinBox {
            padding: 6px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 11px;
            min-height: 18px;
            background-color: white;
            max-width: 80px;
        }
        QSpinBox:focus {
            border-color: #0078d4;
            box-shadow: 0 0 0 2px rgba(0,120,212,0.2);
            outline: none;
        }
        QSpinBox:hover {
            border-color: #adb5bd;
        }
    """)
    config_grid_layout.addWidget(concurrent_count_spin, 0, 1)

    # 登录模式配置
    mode_label = QLabel("登录模式:")
    mode_label.setStyleSheet("""
        QLabel {
            font-size: 11px;
            color: #495057;
            font-weight: 500;
            border: none;
            background: transparent;
            padding: 2px 0;
            min-width: 70px;
        }
    """)
    config_grid_layout.addWidget(mode_label, 1, 0)

    login_mode_combo = QComboBox()
    login_mode_combo.addItem("🚀 并发模式", "concurrent")
    login_mode_combo.addItem("🔄 串行模式", "sequential")
    login_mode_combo.setCurrentIndex(0)
    login_mode_combo.setStyleSheet("""
        QComboBox {
            padding: 6px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 11px;
            min-height: 18px;
            background-color: white;
            max-width: 120px;
        }
        QComboBox:focus {
            border-color: #0078d4;
            box-shadow: 0 0 0 2px rgba(0,120,212,0.2);
            outline: none;
        }
        QComboBox:hover {
            border-color: #adb5bd;
        }
        QComboBox::drop-down {
            width: 20px;
            border: none;
            background: transparent;
        }
        QComboBox::down-arrow {
            width: 10px;
            height: 10px;
        }
    """)
    config_grid_layout.addWidget(login_mode_combo, 1, 1)

    config_layout.addWidget(config_grid_container)
    batch_layout.addWidget(config_container)

    return batch_group

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("批量登录UI优化测试")
        self.setGeometry(100, 100, 400, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        if REAL_WIDGET:
            # 使用真实的AccountWidget
            account_widget = AccountWidget()
            layout.addWidget(account_widget)
        else:
            # 使用模拟的批量登录组件
            batch_widget = create_mock_batch_login_widget()
            layout.addWidget(batch_widget)
            
            # 添加说明标签
            info_label = QLabel("这是批量登录区域的UI优化预览")
            info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
            layout.addWidget(info_label)
        
        layout.addStretch()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f8f9fa;
        }
    """)
    
    window = TestWindow()
    window.show()
    
    print("UI优化测试窗口已启动")
    print("请查看批量登录区域的优化效果：")
    print("1. 渐变背景和阴影效果")
    print("2. 按钮并排显示，节省空间")
    print("3. 配置选项网格布局")
    print("4. 现代化的视觉效果")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
